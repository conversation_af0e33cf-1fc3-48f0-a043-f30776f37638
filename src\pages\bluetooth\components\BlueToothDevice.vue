<template>
    <view class="content">
        <view class="item-01">
            <image 
                src="@/static/products/blood-pressure03.png" 
                class="device-img" 
                mode="aspectFit" 
            />
            <view class="device-desc">
                <view class="flex items-center">
                    <!-- <Electric-quantity style="text-align: left;" :ElectricQuantity="10" :isElectric="true"></Electric-quantity> -->
                    <batteryIcon :batteryLevel="50" width="56rpx" height="23rpx"></batteryIcon>
                    <text v-if="!state.isConnected" style="font-size: 24rpx;padding-left: 14rpx; color: #00DCA7; font-weight: bold;">请将设备开机重试</text>
                </view>
                <view class="title">远程动态血压监测系统</view>
                <view class="connect"> {{ state.isConnected ? state.connectedDevice?.lastId : '未连接' }}</view>
                <view class="tip">不是此台设备</view>
            </view>
            <view class="device-cut" @click="showDeviceList = true">
                <uni-icons custom-prefix="iconfont" type="icon-bangding" size="20" color="white"></uni-icons>
            </view>
        </view>
        
        <view class="item-02" :class="{ 'not-allowed': !state.isConnected }" @click="onClickStart">开始使用</view>
        <!-- 切换设备 -->
        <!-- <u-popup :show="showDeviceList" mode="center"  @close="closeDeviceList" @open="openDeviceList">
            <view>
                <picker-view :indicator-style="indicatorStyle" :value="formData.rjmeasureInterval" @change="bindChangeRJ"
                    class="picker-view">
                    <picker-view-column>
                        <view class="picker-item" v-for="(item, index) in measurementInterval" :key="index">每隔 {{ item.label }} 测量
                        </view>
                    </picker-view-column>
                </picker-view>
            </view>
        </u-popup>
 -->

    </view>
</template>
<script setup lang="ts">
    import ElectricQuantity from '@/components/Electric-quantity.vue';
    import batteryIcon from '@/components/battery-icon.vue'
    import { ref, watch, onMounted } from 'vue'
    
    import useBlueTooth from "@/common/useBlueTooth";
    import { setBlueToothStore } from '@/pages/bluetooth/BlueToothData'

    const BlueTooth = useBlueTooth()
    setBlueToothStore(BlueTooth)

    const { state } = BlueTooth

   
    // uni.showLoading({
    //             title: '加载中...',
    //             mask: true
    //         })

    // uni.hideLoading()

    const onClickStart = async () => {
        // if (!state.isConnected) {
        //     uni.showToast({ title: '未连接上蓝牙！', icon: 'error' });
        //     return
        // }

        // uni.showLoading({
        //     title: '启动中...',
        //     mask: true
        // })
        // const startStatus = await pressure_startMeasurement()

        // uni.hideLoading()

        // if (startStatus) {
        //     uni.showToast({ title: '启动成功！', icon: 'success' });
        // }else {
        //     uni.showToast({ title: '启动失败！', icon: 'error' });
        // }

        uni.navigateTo({
            url: '/pages/bluetooth/proceedMeasurement',
            animationType: "slide-in-right",
            animationDuration: 300,
        });

    }

    // 选择设备
    const showDeviceList = ref(false)

    // 暴露属性给父组件
    defineExpose({
        state
    })

    // function openDeviceList() {
    //     showDeviceList.value = true
    // }

    // function closeDeviceList() {
    //     showDeviceList.value = false
    // }
    // const indicatorStyle = `height: 50px;`

</script>
<style scoped lang="scss">
.content {
    display: flex;
    flex-direction: column;
    height: 720rpx;
    padding: 18rpx;
    overflow: hidden;
    border-radius: 16rpx;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 4rpx 20rpx  rgba(42, 130, 228, 0.15);
    .item-01 {
        position: relative;
        display: flex;
        flex-direction: row;
        flex: 1;
        padding-left: 36rpx;
        padding-right: 24rpx;
        .device-img {
            width: 216rpx;
            height: 100%;
        }
        .device-desc {
            flex: 1;
            overflow: hidden;
            padding-top: 120rpx;
            padding-left: 60rpx;
            .title {
                font-size: 40rpx;
                padding: 10rpx 0;
                font-weight: 500;
            }
            .connect {
                font-size: 40rpx;
                padding-bottom: 10rpx;
            }
            .tip {
                font-size: 20rpx;
                font-weight: 500;
                color: #929292;
                text-decoration: underline;
            }
        }
        .device-cut {
            position: absolute;
            right: 10rpx;
            top: 10rpx;
            background: #00DCA7;
            height: 66rpx;
            width: 66rpx;
            line-height: 66rpx;
            text-align: center;
            border-radius: 50%;
        }
    }
    .item-02 {
        height: 128rpx;
        line-height: 128rpx;
        font-size: 52rpx;
        text-align: center;
        color: white;
        opacity: 1;
        border-radius: 24rpx;
        background: rgba(17, 152, 243, 1);
        margin: 38rpx 32rpx;
        &.not-allowed {
            background: #929292;
        }
    }
}
</style>
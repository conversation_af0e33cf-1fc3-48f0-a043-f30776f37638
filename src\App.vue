<script setup>
	import { onLaunch, onShow, onHide } from "@dcloudio/uni-app";
    import checkAppUpdate from '@/common/checkappupdate';
	onLaunch(() => {
        uni.getSystemInfo({
            success: (res) => {
                // #ifdef APP-PLUS
                checkAppUpdate(res)
                // #endif
            }
        })
		console.log("App Launch");
	});
	onShow(() => {
		console.log("App Show");
	});
	onHide(() => {
		console.log("App Hide");
	});
</script>
<style lang="scss">
	 
	@import "uview-plus/index.scss";

	page {
		background-color: #eee;
	}

    .iconfont{
        font-size: 32rpx;
        display: inline-block;
    }
    .icon-rotating{
        animation: rotating 1.6s linear infinite;
    }
    @keyframes rotating{
        from{
            transform: rotate(0deg);
        }
        to{
            transform: rotate(1turn);
        }
    }

	@import "@/static/iconfont.css";
</style>
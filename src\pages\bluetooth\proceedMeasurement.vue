<template>
    <BarHeight></BarHeight>
    <view class="content" :style="{'height': `calc(100% - 64rpx - ${statusBarHeight}px)`}">
        <view class="header">
            <text>快速测量血压</text>
        </view>
        <view class="line"></view>
        
        <template v-if="!isStart">
            <view class="info">
                <view class="flex justify-between py[20rpx]">
                    <view>
                        <uni-icons type="smallcircle-filled" size="18" color="#808080"></uni-icons>
                        <text>等待启动测量</text>
                    </view>
                    <view class="flex items-center">
                        <uni-icons type="smallcircle-filled" size="18" color="#00DCA7"></uni-icons>
                        <battery-icon :batteryLevel="batteryLevel" width="56rpx" height="23rpx" />
                    </view>
                </view>
                <view class="flex flex-col items-center text-[36rpx]">
                    <text class=" text-center pt-[18rpx]">1、设备连接加压袖带</text>
                    <text class=" text-center pt-[18rpx]">2、请正确佩戴加压袖带</text>
                    <text class="color-[#1198F3] font-700 text-center pt-[18rpx]">完成上述步骤后点击下方图标</text>
                    <text class="color-[#1198F3] font-700 text-center pt-[8rpx]">开启测量</text>
                </view>
            </view>
            <view class="flex-1 flex flex-col">
                <view class="flex-1 bg-[#005bea] mx-[48rpx] mt-[40rpx] mb-[20rpx]">
                    动画
                </view>
                <view class="btn-action" @click="onClickStart">测量血压</view>
            </view>

            <view class="rounded-lg shadow-md bg-[#FAFAFA] p-4 flex flex-row justify-between items-center m-[40rpx]">
                <view></view>
                <view class="flex flex-col justify-center">
                    <view class="text-[#1198F3] font-medium mb-2">·查看连接演示动画</view>
                    <view class="text-[#1198F3] font-medium mb-2">·查看产品规格说明书</view>
                    <view class="text-[#1198F3] font-medium mb-2">·查看产品指示灯信号说明</view>
                    <view class="text-[#1198F3] font-medium mb-2">·需要更多帮助</view>
                </view>
                <view></view>
            </view>
        </template>
        <template v-else>
            <view class="flex justify-between py[20rpx]" style="margin: 0 40rpx; color: #1198F3; margin-bottom: 40rpx;">
                <view>
                    <uni-icons type="smallcircle-filled" size="18" color="#1198F3"></uni-icons>
                    <text>血压测量中...</text>
                </view>
                <view class="flex items-center">
                    <uni-icons type="smallcircle-filled" size="18" color="#00DCA7"></uni-icons>
                    <battery-icon :batteryLevel="batteryLevel" width="56rpx" height="23rpx" />
                </view>
            </view>
            <view class="flex items-center justify-center p-[32rpx] relative">
                <view class="absolute left-[32rpx] top-1/2 transform -translate-y-1/2 writing-vertical text-[32rpx] color-[#1198F3]">
                    血压
                </view>
                <view class="measur-value flex-1 text-center">
                    <view class="flex flex-col items-center">
                        <text class="text-[128rpx] font-bold">0</text>
                        <text class="text-[24rpx] color-[#999] mt-[-10rpx]">mmHg</text>
                    </view>
                </view>
                <AnimationBloodPressure :curMeasuring="curMeasuring" width="26rpx" height="340rpx" barHeight="40rpx"></AnimationBloodPressure>
            </view>

            <view>
                <AnimationECG></AnimationECG>
            </view>
        </template>

	</view>
</template>

<script setup lang="ts">
	import { onMounted, ref, reactive, computed } from 'vue';
    import BarHeight from '@/components/BarHeight.vue';
    import batteryIcon from '@/components/battery-icon.vue'
    import AnimationBloodPressure from '@/components/animation/AnimationBloodPressure.vue';
    import AnimationECG from '@/components/animation/AnimationECG.vue'; // 心电图动画

    import useBlueToothStore from '@/pages/bluetooth/BlueToothData';
    const blueToothStore = useBlueToothStore()
    const { state, pressure_startMeasurement } = blueToothStore

	const batteryLevel = ref(75);

    const statusBarHeight = uni.getSystemInfoSync().statusBarHeight || 0;

    const goBack = () => {
		uni.navigateBack();
	}

    const isStart = ref(true)
    const curMeasuring = ref({
        measureStatus: 2,
        currentBp: 250
    })

    const onClickStart = async () => {
        if (!state.isConnected) {
            uni.showToast({ title: '未连接上蓝牙！', icon: 'error' });
            return
        }

        uni.showLoading({
            title: '启动中...',
            mask: true
        })
        const startStatus = await pressure_startMeasurement()

        uni.hideLoading()

        if (startStatus) {
            uni.showToast({ title: '启动成功！', icon: 'success' });
        }else {
            uni.showToast({ title: '启动失败！', icon: 'error' });
        }
    }
</script>

<style scoped lang="scss">
    page {
        width: 100%;
        height: 100%;
        background: #F7F9FC;
    }
	.content {
        display: flex;
        flex-direction: column;
        background: #FFFFFF;
        box-shadow: 0px 4rpx 20rpx rgba(42, 130, 228, 0.15);
        border-radius: 16rpx;
        width: calc(100% - 64rpx);
        height: calc(100% - 64rpx);
        margin: 32rpx;

        .header {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 116rpx;
            color: #1198F3;
            font-size: 48rpx;
            font-weight: bold;
        }
        .line {
            margin: 0 40rpx;
            height: 1px;
            background: #1198F3;
            box-shadow: 0px 2px 4px  rgba(0, 0, 0, 0.25);
        }
        .info {
            margin: 0 40rpx;
        }

        .btn-action {
            height: 128rpx;
            line-height: 128rpx;
            font-size: 52rpx;
            text-align: center;
            color: white;
            opacity: 1;
            border-radius: 24rpx;
            background: rgba(17, 152, 243, 1);
            margin: 38rpx 32rpx 0;
            &.not-allowed {
                background: #929292;
            }
        }





        .measur-value {
            flex: 1;
            font-size: 128rpx;
        }

        .writing-vertical {
            writing-mode: vertical-rl;
            text-orientation: upright;
        }

        .measur-value {
            flex: 1;
            font-size: 128rpx;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
</style>
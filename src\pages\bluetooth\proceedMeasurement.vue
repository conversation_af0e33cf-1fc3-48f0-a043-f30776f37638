<template>
    <BarHeight></BarHeight>
    <view class="content" :style="{'height': `calc(100% - 64rpx - ${statusBarHeight}px)`}">
        <view class="header">
            <text>快速测量血压</text>
        </view>
        <view class="line"></view>
        
        <template v-if="measureStatus === 1">
            <view class="info">
                <view class="flex justify-between py[20rpx]">
                    <view>
                        <uni-icons type="smallcircle-filled" size="18" color="#808080"></uni-icons>
                        <text>等待启动测量</text>
                    </view>
                    <view class="flex items-center">
                        <uni-icons type="smallcircle-filled" size="18" color="#00DCA7"></uni-icons>
                        <battery-icon :batteryLevel="batteryLevel" width="56rpx" height="23rpx" />
                    </view>
                </view>
                <view class="flex flex-col items-center text-[36rpx]">
                    <text class=" text-center pt-[18rpx]">1、设备连接加压袖带</text>
                    <text class=" text-center pt-[18rpx]">2、请正确佩戴加压袖带</text>
                    <text class="color-[#1198F3] font-700 text-center pt-[18rpx]">完成上述步骤后点击下方图标</text>
                    <text class="color-[#1198F3] font-700 text-center pt-[8rpx]">开启测量</text>
                </view>
            </view>
            <view class="flex-1 flex flex-col">
                <view class="flex-1 bg-[#FAFAFA] mx-[48rpx] mt-[40rpx] mb-[20rpx]">
                    
                </view>
                <view class="btn-action" @click="onClickStart">测量血压</view>
            </view>

            <view class="rounded-lg shadow-md bg-[#FAFAFA] p-4 flex flex-row justify-between items-center m-[40rpx]">
                <view></view>
                <view class="flex flex-col justify-center">
                    <view class="text-[#1198F3] font-medium mb-2">·查看连接演示动画</view>
                    <view class="text-[#1198F3] font-medium mb-2">·查看产品规格说明书</view>
                    <view class="text-[#1198F3] font-medium mb-2">·查看产品指示灯信号说明</view>
                    <view class="text-[#1198F3] font-medium mb-2">·需要更多帮助</view>
                </view>
                <view></view>
            </view>
        </template>
        <template v-else-if="measureStatus === 2">
            <view class="flex justify-between py[20rpx]" style="margin: 0 40rpx; color: #1198F3; margin-bottom: 40rpx;">
                <view>
                    <uni-icons type="smallcircle-filled" size="18" color="#1198F3"></uni-icons>
                    <text>血压测量中...</text>
                </view>
                <view class="flex items-center">
                    <uni-icons type="smallcircle-filled" size="18" color="#00DCA7"></uni-icons>
                    <battery-icon :batteryLevel="batteryLevel" width="56rpx" height="23rpx" />
                </view>
            </view>
            <view class="flex items-center justify-center p-[32rpx] relative">
                <view class="absolute left-[32rpx] top-1/2 transform -translate-y-1/2 writing-vertical text-[32rpx] color-[#1198F3]">
                    血压
                </view>
                <view class="measur-value flex-1 text-center">
                    <view class="flex flex-col items-center">
                        <text class="text-[128rpx] font-bold">{{ curMeasuring.currentBp }}</text>
                        <text class="text-[24rpx] color-[#999] mt-[-10rpx]">mmHg</text>
                    </view>
                </view>
                <AnimationBloodPressure :curMeasuring="curMeasuring" width="26rpx" height="340rpx" barHeight="40rpx"></AnimationBloodPressure>
            </view>

            <view class="mt-[80rpx]">
                <AnimationECG v-if="curMeasuring.currentBp"></AnimationECG>
            </view>

            <view class="btn-stop-container">
                <!-- 停止按钮 -->
                <view class="btn-action btn-stop"
                      @touchstart="onTouchStart"
                      @touchend="onTouchEnd"
                      :class="{ 'btn-pressing': isPressing }">
                    <view class="stop-icon">
                        <view class="stop-square"></view>
                    </view>
                    <text class="stop-text">长按2s停止</text>
                    <!-- 长按进度环 -->
                    <view class="progress-ring" v-if="isPressing">
                        <view class="progress-fill" :style="{ animationDuration: (longPressDuration + 1000) + 'ms' }"></view>
                    </view>
                </view>
            </view>

        </template>
        <template v-else-if="measureStatus === 3">
            <view class="flex justify-between py[20rpx]" style="margin: 0 40rpx; color: #1198F3; margin-bottom: 40rpx;">
                <view>
                    <uni-icons type="smallcircle-filled" size="18" color="#1198F3"></uni-icons>
                    <text>血压测量已完成...</text>
                </view>
                <view class="flex items-center">
                    <uni-icons type="smallcircle-filled" size="18" color="#00DCA7"></uni-icons>
                    <battery-icon :batteryLevel="batteryLevel" width="56rpx" height="23rpx" />
                </view>
            </view>

            <view v-if="!errorStatus.hasError" class="measure-content">
                <view class="flex justify-between items-center pb-[50rpx] pt-[30rpx] flex-1" style="border-bottom: 1px solid #FFFFFF;">
                    <text class="text-white text-2xl font-bold self-baseline pt-[20rpx]">血压:</text>
                    <view class="text-white text-4xl font-bold flex flex-col">
                        <text>{{ measurementResult.systolic }}/{{ measurementResult.diastolic }}</text>
                        <text class="text-sm text-right">mmHg</text>
                    </view>
                </view>
                <view class="flex justify-between items-center pb-[50rpx] pt-[30rpx] flex-1">
                    <text class="text-white text-2xl font-bold self-baseline pt-[20rpx]">脉搏:</text>
                    <view class="text-white text-4xl font-bold flex flex-col">
                        <text>{{ measurementResult.heartRate }}</text>
                        <text class="text-sm text-right">bpm</text>
                    </view>
                </view>

                <!-- 时间和历史数据按钮 -->
                <view class="flex justify-between items-center mb-3">
                    <view class="text-white flex-1">
                        <text class="block">16:40</text>
                        <text class="block">2025.07.15</text>
                    </view>
                    <view class="bg-white rounded-full text-black px-[32rpx] py-[16rpx] font-500 text-[32rpx]">更多历史数据</view>
                </view>
            </view>
            <view v-else class="measure-content" style="background: #FAFAFA;">
                <!-- 错误状态显示 -->
                <view class="error-info">
                    <text class="error-title">{{ getErrorCodeInfo(errorStatus.errorCode).title }}</text>
                    <image 
                        src="@/static/err.png" 
                        class="w-[160rpx] h-[180rpx]" 
                        mode="aspectFit" 
                    />
                    <text class="error-message">{{ getErrorCodeInfo(errorStatus.errorCode).message }}</text>
                    <text v-if="getErrorCodeInfo(errorStatus.errorCode).suggestion" class="error-suggestion">
                        建议：{{ getErrorCodeInfo(errorStatus.errorCode).suggestion }}
                    </text>
                </view>
            </view>



            <view class="flex">
                <view class="btn-action flex-1 btn-mini" @click="onClickStart">再次测量</view>
                <view class="btn-action flex-1 btn-mini" style="background: #00DCA7;" @click="goBack">返回首页</view>
            </view>
        </template>

	</view>
</template>

<script setup lang="ts">
	import { onMounted, onUnmounted, ref, reactive, computed } from 'vue';
    import BarHeight from '@/components/BarHeight.vue';
    import batteryIcon from '@/components/battery-icon.vue'
    import AnimationBloodPressure from '@/components/animation/AnimationBloodPressure.vue';
    import AnimationECG from '@/components/animation/AnimationECG.vue'; // 心电图动画

    import useBlueToothStore from '@/pages/bluetooth/BlueToothData';
    import { type BluetoothCallbackData } from "@/common/useBlueTooth";
    import {
        getErrorCodeInfo,
        showErrorCodeToast,
        getErrorCodeColor,
        isCriticalError
    } from '@/utils/errorCodeHandler';

    const blueToothStore = useBlueToothStore()
    const { state, pressure_startMeasurement, pressure_stopMeasurement, onBluetoothData } = blueToothStore

	const batteryLevel = ref(0);

    const statusBarHeight = uni.getSystemInfoSync().statusBarHeight || 0;

    const goBack = () => {
		uni.navigateBack();
	}

    // 页面状态，1 = 启动测量页面， 2 = 测量中， 3 = 测量完成
    const measureStatus = ref(1)

    const curMeasuring = ref({
        measureStatus: 2,
        currentBp: 0
    })

    // 测量结果状态
    const measurementResult = ref({
        systolic: 0,      // 收缩压
        diastolic: 0,     // 舒张压
        heartRate: 0,     // 心率
        heartStatus: 0,   // 心跳状态
        errorCode: 0,     // 错误码
        timestamp: 0      // 时间戳
    })

    // 错误状态
    const errorStatus = ref({
        hasError: false,
        errorCode: 0,
    })

    // 长按相关状态
    const isPressing = ref(false)
    const longPressDuration = ref(2000) // 长按持续时间 1.5秒
    let longPressTimer: any = null

    const onClickStart = async () => {


        console.log(state.isConnected)
        if (!state.isConnected) {
            uni.showToast({ title: '未连接上蓝牙！', icon: 'error' });
            return
        }

        uni.showLoading({
            title: '启动中...',
            mask: true
        })
        const startStatus = await pressure_startMeasurement()

        uni.hideLoading()

        if (startStatus) {
            uni.showToast({ title: '启动成功！', icon: 'success' });

            measureStatus.value = 2;
            curMeasuring.value = {
                measureStatus: 2,
                currentBp: 0
            }
        }else {
            uni.showToast({ title: '启动失败！', icon: 'error' });
        }
    }

    const onClickStop = async () => {
        if (!state.isConnected) {
            uni.showToast({ title: '未连接上蓝牙！', icon: 'error' });
            return
        }

        uni.showLoading({
            title: '停止中...',
            mask: true
        })
        const startStatus = await pressure_stopMeasurement()

        uni.hideLoading()

        if (startStatus) {
            measureStatus.value = 1
            uni.showToast({ title: '停止成功！', icon: 'success' });
        }else {
            uni.showToast({ title: '停止失败！', icon: 'error' });
        }
    }

    // 长按开始
    const onTouchStart = () => {
        isPressing.value = true

        // 设置长按定时器
        longPressTimer = setTimeout(() => {
            // 长按时间到达，触发停止
            onLongPressStop()
        }, longPressDuration.value)
    }

    // 长按结束
    const onTouchEnd = () => {
        isPressing.value = false

        // 清除定时器
        if (longPressTimer) {
            clearTimeout(longPressTimer)
            longPressTimer = null
        }
    }

    // 长按停止测量
    const onLongPressStop = async () => {
        console.log('长按停止测量')
        isPressing.value = false

        // 清除定时器
        if (longPressTimer) {
            clearTimeout(longPressTimer)
            longPressTimer = null
        }

        // 执行停止测量
        await onClickStop()
    }
    // 处理测量结果的函数
    const handleMeasurementResult = (data: any) => {
        console.log('处理测量结果:', data);

        // 更新测量结果
        measurementResult.value = {
            systolic: data.systolic || 0,
            diastolic: data.diastolic || 0,
            heartRate: data.heartRate || 0,
            heartStatus: data.heartStatus || 0,
            errorCode: data.errorCode || 0,
            timestamp: data.timestamp || Date.now()
        };

        // 处理错误码
        const errorCode = data.errorCode || 0;

        if (errorCode === 0) {
            // 测量成功
            errorStatus.value = {
                hasError: false,
                errorCode: 0
            };
            measureStatus.value = 3; // 测量完成
            showErrorCodeToast(errorCode);

            // 调用存储接口，现在先存储到app
            
        } else {
            // 测量出现错误
            errorStatus.value = {
                hasError: true,
                errorCode: errorCode
            };

            // 如果是严重错误，停止测量
            if (isCriticalError(errorCode)) {
                measureStatus.value = 1; // 停止测量
            }

            // 显示错误提示
            showErrorCodeToast(errorCode);
        }
    };

    // #ifdef APP-PLUS
    onBluetoothData((measuData: BluetoothCallbackData) => {
        console.log('组件获取到的数据:', measuData)

        if (measuData.deviceName === 'HYO2' || measuData.deviceName === 'SPHY') {
            if (measuData.packType === 1 && measuData.len === 2) {
                // 袖带压数据
                curMeasuring.value = {
                    measureStatus: 2,
                    currentBp: measuData.data?.pressure || 0
                }
            } else if (measuData.packType === 1 && measuData.len === 1) {
                // 启动测量回复
                const result = measuData.data?.result;
                if (result === 0) {
                    console.log('测量启动成功');
                    measureStatus.value = 2;
                    curMeasuring.value = {
                        measureStatus: 2,
                        currentBp: 0
                    }
                } else if (result === 0xFF) {
                    uni.showToast({ title: '启动失败，设备正忙', icon: 'error' });
                } else if (result === 0xFE) {
                    uni.showToast({ title: '启动失败，功能不支持', icon: 'error' });
                }
            } else if ((measuData.packType === 2 || measuData.packType === 4) && measuData.len === 8) {
                // 测量结果
                handleMeasurementResult(measuData.data);
            } else if (measuData.packType === 1 && measuData.len === 3) {
                // 电池信息
                const batteryData = measuData.data;
                if (batteryData?.percentage) {
                    batteryLevel.value = batteryData.percentage;
                }
            }
        }
    })
    // #endif

    // 组件卸载时清理定时器
    onUnmounted(() => {
        if (longPressTimer) {
            clearTimeout(longPressTimer)
            longPressTimer = null
        }
    })

</script>

<style scoped lang="scss">
    page {
        width: 100%;
        height: 100%;
        background: #F7F9FC;
    }
	.content {
        position: relative;
        display: flex;
        flex-direction: column;
        background: #FFFFFF;
        box-shadow: 0px 4rpx 20rpx rgba(42, 130, 228, 0.15);
        border-radius: 16rpx;
        width: calc(100% - 64rpx);
        height: calc(100% - 64rpx);
        margin: 32rpx;

        .header {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 116rpx;
            color: #1198F3;
            font-size: 48rpx;
            font-weight: bold;
        }
        .line {
            margin: 0 40rpx;
            height: 1px;
            background: #1198F3;
            box-shadow: 0px 2px 4px  rgba(0, 0, 0, 0.25);
        }
        .info {
            margin: 0 40rpx;
        }

        .btn-action {
            height: 128rpx;
            line-height: 128rpx;
            font-size: 52rpx;
            text-align: center;
            color: white;
            opacity: 1;
            border-radius: 24rpx;
            background: rgba(17, 152, 243, 1);
            margin: 38rpx 32rpx 0;
            &.not-allowed {
                background: #929292;
            }
            &.btn-mini {
                height: 100rpx;
                line-height: 100rpx;
                border-radius: 36rpx;
            }
        }

        .btn-stop-container {
            position: fixed;
            bottom: 120rpx;
            left: 50%;
            transform: translateX(-50%);
            z-index: 100;
        }

        .btn-stop {
            width: 180rpx;
            height: 180rpx;
            border-radius: 50%;
            background: linear-gradient(135deg, #FF6B6B 0%, #FF4757 100%);
            box-shadow: 0 8rpx 24rpx rgba(255, 71, 87, 0.4),
                        0 0 0 8rpx rgba(255, 71, 87, 0.1),
                        0 0 0 16rpx rgba(255, 71, 87, 0.05);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            transition: all 0.3s ease;
            animation: pulse-warning 2s infinite;
            margin: 0;
            line-height: normal;

            &.btn-pressing {
                transform: scale(0.95);
                animation: pulse-pressing 0.5s infinite;
                box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.8);
            }

            .stop-icon {
                margin-bottom: 8rpx;

                .stop-square {
                    width: 24rpx;
                    height: 24rpx;
                    background: white;
                    border-radius: 4rpx;
                }
            }

            .stop-text {
                font-size: 28rpx;
                color: white;
                font-weight: 600;
                text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
            }

            // 长按进度环
            .progress-ring {
                position: absolute;
                top: -24rpx;
                left: -24rpx;
                right: -24rpx;
                bottom: -24rpx;
                border-radius: 50%;

                .progress-fill {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    border-radius: 50%;
                    border: 8rpx solid transparent;
                    border-top-color: #00FF88;
                    border-right-color: #00FF88;
                    animation: progress-rotate linear;
                    filter: drop-shadow(0 0 8rpx rgba(0, 255, 136, 0.8));

                    &::before {
                        content: '';
                        position: absolute;
                        top: -8rpx;
                        left: -8rpx;
                        right: -8rpx;
                        bottom: -8rpx;
                        border-radius: 50%;
                        border: 8rpx solid rgba(255, 255, 255, 0.2);
                        z-index: -1;
                    }

                    &::after {
                        content: '';
                        position: absolute;
                        top: 8rpx;
                        left: 8rpx;
                        right: 8rpx;
                        bottom: 8rpx;
                        border-radius: 50%;
                        background: linear-gradient(135deg, #FF6B6B 0%, #FF4757 100%);
                        z-index: -2;
                    }
                }
            }
        }

        @keyframes pulse-warning {
            0% {
                box-shadow: 0 8rpx 24rpx rgba(255, 71, 87, 0.4),
                            0 0 0 8rpx rgba(255, 71, 87, 0.1),
                            0 0 0 16rpx rgba(255, 71, 87, 0.05);
            }
            50% {
                box-shadow: 0 8rpx 24rpx rgba(255, 71, 87, 0.6),
                            0 0 0 12rpx rgba(255, 71, 87, 0.15),
                            0 0 0 24rpx rgba(255, 71, 87, 0.08);
            }
            100% {
                box-shadow: 0 8rpx 24rpx rgba(255, 71, 87, 0.4),
                            0 0 0 8rpx rgba(255, 71, 87, 0.1),
                            0 0 0 16rpx rgba(255, 71, 87, 0.05);
            }
        }

        @keyframes pulse-pressing {
            0% {
                box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.8);
            }
            50% {
                box-shadow: 0 8rpx 20rpx rgba(255, 71, 87, 1);
            }
            100% {
                box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.8);
            }
        }

        @keyframes progress-rotate {
            0% {
                transform: rotate(0deg);
                border-top-color: #00FF88;
                border-right-color: #00FF88;
                border-bottom-color: transparent;
                border-left-color: transparent;
            }
            25% {
                border-top-color: #00FF88;
                border-right-color: #00FF88;
                border-bottom-color: transparent;
                border-left-color: transparent;
            }
            25.1% {
                border-top-color: #00FF88;
                border-right-color: #00FF88;
                border-bottom-color: #00FF88;
                border-left-color: transparent;
            }
            50% {
                border-top-color: #00FF88;
                border-right-color: #00FF88;
                border-bottom-color: #00FF88;
                border-left-color: transparent;
            }
            50.1% {
                border-top-color: #00FF88;
                border-right-color: #00FF88;
                border-bottom-color: #00FF88;
                border-left-color: #00FF88;
            }
            75% {
                border-top-color: #00FF88;
                border-right-color: #00FF88;
                border-bottom-color: #00FF88;
                border-left-color: #00FF88;
            }
            100% {
                transform: rotate(360deg);
                border-top-color: #00FF88;
                border-right-color: #00FF88;
                border-bottom-color: #00FF88;
                border-left-color: #00FF88;
            }
        }


        // 重试按钮样式
        .btn-retry {
            width: 180rpx;
            height: 180rpx;
            border-radius: 50%;
            background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
            box-shadow: 0 8rpx 24rpx rgba(82, 196, 26, 0.4),
                        0 0 0 8rpx rgba(82, 196, 26, 0.1),
                        0 0 0 16rpx rgba(82, 196, 26, 0.05);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            transition: all 0.3s ease;
            animation: pulse-success 2s infinite;
            margin: 0;
            line-height: normal;

            &:active {
                transform: scale(0.95);
                box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.6);
            }

            .retry-icon {
                margin-bottom: 8rpx;

                .retry-text {
                    font-size: 36rpx;
                    color: white;
                    font-weight: bold;
                }
            }

            .retry-text {
                font-size: 28rpx;
                color: white;
                font-weight: 600;
                text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
            }
        }

        @keyframes pulse-success {
            0% {
                box-shadow: 0 8rpx 24rpx rgba(82, 196, 26, 0.4),
                            0 0 0 8rpx rgba(82, 196, 26, 0.1),
                            0 0 0 16rpx rgba(82, 196, 26, 0.05);
            }
            50% {
                box-shadow: 0 8rpx 24rpx rgba(82, 196, 26, 0.6),
                            0 0 0 12rpx rgba(82, 196, 26, 0.15),
                            0 0 0 24rpx rgba(82, 196, 26, 0.08);
            }
            100% {
                box-shadow: 0 8rpx 24rpx rgba(82, 196, 26, 0.4),
                            0 0 0 8rpx rgba(82, 196, 26, 0.1),
                            0 0 0 16rpx rgba(82, 196, 26, 0.05);
            }
        }





        .measur-value {
            flex: 1;
            font-size: 128rpx;
        }

        .writing-vertical {
            writing-mode: vertical-rl;
            text-orientation: upright;
        }

        .measur-value {
            flex: 1;
            font-size: 128rpx;
            display: flex;
            align-items: center;
            justify-content: center;
        }


        .measure-content {
            display: flex;
            flex-direction: column;
            border-radius: 32rpx;
            background: rgba(0, 220, 167, 1);
            box-shadow: 0px 4rpx 20rpx  rgba(42, 130, 228, 0.15);
            margin: 0 40rpx;
            height: 580rpx;
            padding: 36rpx;
            padding-bottom: 0;
            box-sizing: border-box;
            color: white;


            .error-info {
                display: flex;
                flex-direction: column;
                align-items: center;
                text-align: center;

                .error-title {
                    color: #D43030;
                    font-size: 36rpx;
                    font-weight: 600;
                    margin-bottom: 40rpx;
                }

                .error-message {
                    color: #1198F3;
                    font-size: 28rpx;
                    margin-bottom: 8rpx;
                    line-height: 1.4;
                    padding: 30rpx 0;
                }

                .error-suggestion {
                    font-size: 28rpx;
                    color: #000000;
                    line-height: 1.4;
                }
            }
        }
    }
</style>
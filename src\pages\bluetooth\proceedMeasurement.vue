<template>
    <BarHeight></BarHeight>
    <view class="content" :style="{'height': `calc(100% - 64rpx - ${statusBarHeight}px)`}">
        <view class="header">
            <text>快速测量血压</text>
        </view>
        <view class="line"></view>
        
        <template v-if="measureStatus === 1">
            <view class="info">
                <view class="flex justify-between py[20rpx]">
                    <view>
                        <uni-icons type="smallcircle-filled" size="18" color="#808080"></uni-icons>
                        <text>等待启动测量</text>
                    </view>
                    <view class="flex items-center">
                        <uni-icons type="smallcircle-filled" size="18" color="#00DCA7"></uni-icons>
                        <battery-icon :batteryLevel="batteryLevel" width="56rpx" height="23rpx" />
                    </view>
                </view>
                <view class="flex flex-col items-center text-[36rpx]">
                    <text class=" text-center pt-[18rpx]">1、设备连接加压袖带</text>
                    <text class=" text-center pt-[18rpx]">2、请正确佩戴加压袖带</text>
                    <text class="color-[#1198F3] font-700 text-center pt-[18rpx]">完成上述步骤后点击下方图标</text>
                    <text class="color-[#1198F3] font-700 text-center pt-[8rpx]">开启测量</text>
                </view>
            </view>
            <view class="flex-1 flex flex-col">
                <view class="flex-1 bg-[#005bea] mx-[48rpx] mt-[40rpx] mb-[20rpx]">
                    动画
                </view>
                <view class="btn-action" @click="onClickStart">测量血压</view>
            </view>

            <view class="rounded-lg shadow-md bg-[#FAFAFA] p-4 flex flex-row justify-between items-center m-[40rpx]">
                <view></view>
                <view class="flex flex-col justify-center">
                    <view class="text-[#1198F3] font-medium mb-2">·查看连接演示动画</view>
                    <view class="text-[#1198F3] font-medium mb-2">·查看产品规格说明书</view>
                    <view class="text-[#1198F3] font-medium mb-2">·查看产品指示灯信号说明</view>
                    <view class="text-[#1198F3] font-medium mb-2">·需要更多帮助</view>
                </view>
                <view></view>
            </view>
        </template>
        <template v-else-if="measureStatus === 2">
            <view class="flex justify-between py[20rpx]" style="margin: 0 40rpx; color: #1198F3; margin-bottom: 40rpx;">
                <view>
                    <uni-icons type="smallcircle-filled" size="18" color="#1198F3"></uni-icons>
                    <text>血压测量中...</text>
                </view>
                <view class="flex items-center">
                    <uni-icons type="smallcircle-filled" size="18" color="#00DCA7"></uni-icons>
                    <battery-icon :batteryLevel="batteryLevel" width="56rpx" height="23rpx" />
                </view>
            </view>
            <view class="flex items-center justify-center p-[32rpx] relative">
                <view class="absolute left-[32rpx] top-1/2 transform -translate-y-1/2 writing-vertical text-[32rpx] color-[#1198F3]">
                    血压
                </view>
                <view class="measur-value flex-1 text-center">
                    <view class="flex flex-col items-center">
                        <text class="text-[128rpx] font-bold">{{ curMeasuring.currentBp }}</text>
                        <text class="text-[24rpx] color-[#999] mt-[-10rpx]">mmHg</text>
                    </view>
                </view>
                <AnimationBloodPressure :curMeasuring="curMeasuring" width="26rpx" height="340rpx" barHeight="40rpx"></AnimationBloodPressure>
            </view>

            <view class="mt-[80rpx]">
                <AnimationECG></AnimationECG>
            </view>

            <view class="btn-stop-container">
                <view class="btn-action btn-stop" @click="onClickStop">
                    <view class="stop-icon">
                        <view class="stop-square"></view>
                    </view>
                    <text class="stop-text">停止测量</text>
                </view>
            </view>
        </template>
        <template v-else-if="measureStatus === 3">
            <view class="flex justify-between py[20rpx]" style="margin: 0 40rpx; color: #1198F3; margin-bottom: 40rpx;">
                <view>
                    <uni-icons type="smallcircle-filled" size="18" color="#1198F3"></uni-icons>
                    <text>血压测量已完成...</text>
                </view>
                <view class="flex items-center">
                    <uni-icons type="smallcircle-filled" size="18" color="#00DCA7"></uni-icons>
                    <battery-icon :batteryLevel="batteryLevel" width="56rpx" height="23rpx" />
                </view>
            </view>

            <view class="measure-content">
                <view class="flex justify-between items-center pb-[50rpx] pt-[30rpx] flex-1" style="border-bottom: 1px solid #FFFFFF;">
                    <text class="text-white text-2xl font-bold self-baseline pt-[20rpx]">血压:</text>
                    <view class="text-white text-4xl font-bold flex flex-col">
                        <text>175/75</text>
                        <text class="text-sm text-right">mmHg</text>
                    </view>
                </view>
                <view class="flex justify-between items-center pb-[50rpx] pt-[30rpx] flex-1">
                    <text class="text-white text-2xl font-bold self-baseline pt-[20rpx]">脉搏:</text>
                    <view class="text-white text-4xl font-bold flex flex-col">
                        <text>33</text>
                        <text class="text-sm text-right">bpm</text>
                    </view>
                </view>

                <!-- 时间和历史数据按钮 -->
                <view class="flex justify-between items-center mb-3">
                    <view class="text-white flex-1">
                        <text class="block">16:40</text>
                        <text class="block">2025.07.15</text>
                    </view>
                    <view class="bg-white rounded-full text-black px-[32rpx] py-[16rpx] font-500 text-[32rpx]">更多历史数据</view>
                </view>
            </view>
            <view class="flex">
                <view class="btn-action flex-1 btn-mini">再次测量</view>
                <view class="btn-action flex-1 btn-mini" style="background: #00DCA7;">返回首页</view>
            </view>
        </template>

	</view>
</template>

<script setup lang="ts">
	import { onMounted, ref, reactive, computed } from 'vue';
    import BarHeight from '@/components/BarHeight.vue';
    import batteryIcon from '@/components/battery-icon.vue'
    import AnimationBloodPressure from '@/components/animation/AnimationBloodPressure.vue';
    import AnimationECG from '@/components/animation/AnimationECG.vue'; // 心电图动画

    import useBlueToothStore from '@/pages/bluetooth/BlueToothData';
    import { type BluetoothCallbackData } from "@/common/useBlueTooth";
    const blueToothStore = useBlueToothStore()
    const { state, pressure_startMeasurement, pressure_stopMeasurement, onBluetoothData } = blueToothStore

	const batteryLevel = ref(75);

    const statusBarHeight = uni.getSystemInfoSync().statusBarHeight || 0;

    const goBack = () => {
		uni.navigateBack();
	}

    const measureStatus = ref(3)
    const curMeasuring = ref({
        measureStatus: 2,
        currentBp: 0
    })

    const onClickStart = async () => {


        console.log(state.isConnected)
        if (!state.isConnected) {
            uni.showToast({ title: '未连接上蓝牙！', icon: 'error' });
            return
        }

        uni.showLoading({
            title: '启动中...',
            mask: true
        })
        const startStatus = await pressure_startMeasurement()

        uni.hideLoading()

        if (startStatus) {
            measureStatus.value = 2
            uni.showToast({ title: '启动成功！', icon: 'success' });
        }else {
            uni.showToast({ title: '启动失败！', icon: 'error' });
        }
    }

    const onClickStop = async () => {
        if (!state.isConnected) {
            uni.showToast({ title: '未连接上蓝牙！', icon: 'error' });
            return
        }

        uni.showLoading({
            title: '停止中...',
            mask: true
        })
        const startStatus = await pressure_stopMeasurement()

        uni.hideLoading()

        if (startStatus) {
            measureStatus.value = 1
            uni.showToast({ title: '停止成功！', icon: 'success' });
        }else {
            uni.showToast({ title: '停止失败！', icon: 'error' });
        }
    }
    // #ifdef APP-PLUS
    onBluetoothData((measuData: BluetoothCallbackData) => {
        console.log('组件获取到的数据:', measuData)

        if (measuData.deviceName === 'HYO2') {
            if (measuData.packType === 1 && measuData.len === 2) {
                // 脉搏
                curMeasuring.value = {
                    measureStatus: 2,
                    currentBp: measuData.data?.pressure || 0
                }
            }else if (measuData.packType === 2 && measuData.len === 8) {
                // 返回结果

            }
        }
    })
    // #endif

</script>

<style scoped lang="scss">
    page {
        width: 100%;
        height: 100%;
        background: #F7F9FC;
    }
	.content {
        position: relative;
        display: flex;
        flex-direction: column;
        background: #FFFFFF;
        box-shadow: 0px 4rpx 20rpx rgba(42, 130, 228, 0.15);
        border-radius: 16rpx;
        width: calc(100% - 64rpx);
        height: calc(100% - 64rpx);
        margin: 32rpx;

        .header {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 116rpx;
            color: #1198F3;
            font-size: 48rpx;
            font-weight: bold;
        }
        .line {
            margin: 0 40rpx;
            height: 1px;
            background: #1198F3;
            box-shadow: 0px 2px 4px  rgba(0, 0, 0, 0.25);
        }
        .info {
            margin: 0 40rpx;
        }

        .btn-action {
            height: 128rpx;
            line-height: 128rpx;
            font-size: 52rpx;
            text-align: center;
            color: white;
            opacity: 1;
            border-radius: 24rpx;
            background: rgba(17, 152, 243, 1);
            margin: 38rpx 32rpx 0;
            &.not-allowed {
                background: #929292;
            }
            &.btn-mini {
                height: 100rpx;
                line-height: 100rpx;
                border-radius: 36rpx;
            }
        }

        .btn-stop-container {
            position: fixed;
            bottom: 120rpx;
            left: 50%;
            transform: translateX(-50%);
            z-index: 100;
        }

        .btn-stop {
            width: 180rpx;
            height: 180rpx;
            border-radius: 50%;
            background: linear-gradient(135deg, #FF6B6B 0%, #FF4757 100%);
            box-shadow: 0 8rpx 24rpx rgba(255, 71, 87, 0.4),
                        0 0 0 8rpx rgba(255, 71, 87, 0.1),
                        0 0 0 16rpx rgba(255, 71, 87, 0.05);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            transition: all 0.3s ease;
            animation: pulse-warning 2s infinite;
            margin: 0;
            line-height: normal;

            &:active {
                transform: scale(0.95);
                box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.6);
            }

            .stop-icon {
                margin-bottom: 8rpx;

                .stop-square {
                    width: 24rpx;
                    height: 24rpx;
                    background: white;
                    border-radius: 4rpx;
                }
            }

            .stop-text {
                font-size: 28rpx;
                color: white;
                font-weight: 600;
                text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
            }
        }

        @keyframes pulse-warning {
            0% {
                box-shadow: 0 8rpx 24rpx rgba(255, 71, 87, 0.4),
                            0 0 0 8rpx rgba(255, 71, 87, 0.1),
                            0 0 0 16rpx rgba(255, 71, 87, 0.05);
            }
            50% {
                box-shadow: 0 8rpx 24rpx rgba(255, 71, 87, 0.6),
                            0 0 0 12rpx rgba(255, 71, 87, 0.15),
                            0 0 0 24rpx rgba(255, 71, 87, 0.08);
            }
            100% {
                box-shadow: 0 8rpx 24rpx rgba(255, 71, 87, 0.4),
                            0 0 0 8rpx rgba(255, 71, 87, 0.1),
                            0 0 0 16rpx rgba(255, 71, 87, 0.05);
            }
        }





        .measur-value {
            flex: 1;
            font-size: 128rpx;
        }

        .writing-vertical {
            writing-mode: vertical-rl;
            text-orientation: upright;
        }

        .measur-value {
            flex: 1;
            font-size: 128rpx;
            display: flex;
            align-items: center;
            justify-content: center;
        }


        .measure-content {
            display: flex;
            flex-direction: column;
            border-radius: 32rpx;
            background: rgba(0, 220, 167, 1);
            box-shadow: 0px 4rpx 20rpx  rgba(42, 130, 228, 0.15);
            margin: 0 40rpx;
            height: 580rpx;
            padding: 36rpx;
            padding-bottom: 0;
            box-sizing: border-box;
            color: white;
        }
    }
</style>
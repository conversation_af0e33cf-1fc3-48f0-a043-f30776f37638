<template>
    <view class="data-item">
        <!-- 测量时间和状态 -->
        <view class="item-header">
            <view class="measure-time">{{ item.measureTime }}</view>

            <view class="flex items-center">
                <!-- 设备信息 -->
                <view class="device-info">
                    <view class="device-name">
                        <!-- <text class="device-icon">📱</text> -->
                        <text>{{ item.deviceId?.slice(-4) || '-' }}</text>
                    </view>
                </view>
                <view class="status-badge" :class="statusClass">
                    {{ statusText }}
                </view>
            </view>
        </view>
        
        <!-- 测量结果 -->
        <view v-if="item.errorCode === 0" class="measurement-result">
            <view class="blood-pressure">
                <view class="bp-values">
                    <text class="systolic">{{ item.systolic }}</text>
                    <text class="separator">/</text>
                    <text class="diastolic">{{ item.diastolic }}</text>
                    <text class="unit">mmHg</text>
                </view>
                <view class="bp-level" :style="{ color: levelColor }">
                    {{ bloodPressureLevel }}
                </view>
            </view>
            
            <view class="heart-rate">
                <view class="hr-icon">♥</view>
                <view class="hr-value">
                    <text class="hr-number">{{ item.heartRate }}</text>
                    <text class="hr-unit">bpm</text>
                </view>
            </view>
        </view>
        
        <!-- 错误信息 -->
        <view v-else class="error-result">
            <view class="error-icon">⚠</view>
            <view class="error-info">
                <text class="error-title">{{ getErrorCodeInfo(item.errorCode).title }}</text>
                <text class="error-message">{{ getErrorCodeInfo(item.errorCode).message }}</text>
            </view>
        </view>
       
        
        <!-- 操作按钮 -->
        <view class="item-actions">
            <!-- <view class="action-btn share-btn" @click="shareData">
                <text class="btn-icon">📤</text>
                <text class="btn-text">分享</text>
            </view> -->
            <view class="action-btn delete-btn ml-[20rpx]" @click="deleteData">
                <text class="btn-icon">🗑</text>
                <text class="btn-text">删除</text>
            </view>
        </view>
    </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { getErrorCodeInfo } from '@/utils/errorCodeHandler';
import { deleteMeasurementData, type MeasurementData } from '@/utils/measurementCache';

const props = defineProps<{
    item: MeasurementData;
}>();

const emit = defineEmits<{
    refresh: [];
}>();

// 状态相关计算属性
const statusClass = computed(() => {
    return props.item.errorCode === 0 ? 'status-success' : 'status-error';
});

const statusText = computed(() => {
    return props.item.errorCode === 0 ? '测量成功' : '测量异常';
});

// 血压等级计算
const bloodPressureLevel = computed(() => {
    const { systolic, diastolic } = props.item;
    
    if (systolic < 90 || diastolic < 60) {
        return '低血压';
    } else if (systolic < 120 && diastolic < 80) {
        return '正常';
    } else if (systolic < 130 && diastolic < 80) {
        return '正常高值';
    } else if (systolic < 140 || diastolic < 90) {
        return '1级高血压';
    } else if (systolic < 160 || diastolic < 100) {
        return '2级高血压';
    } else {
        return '3级高血压';
    }
});

// 血压等级颜色
const levelColor = computed(() => {
    const level = bloodPressureLevel.value;
    
    if (level === '正常') {
        return '#52c41a';
    } else if (level === '正常高值') {
        return '#faad14';
    } else if (level.includes('高血压')) {
        return '#ff4d4f';
    } else if (level === '低血压') {
        return '#1890ff';
    } else {
        return '#666666';
    }
});

// 分享数据
const shareData = () => {
    const shareText = props.item.errorCode === 0 
        ? `血压测量结果\n时间：${props.item.measureTime}\n血压：${props.item.systolic}/${props.item.diastolic} mmHg\n心率：${props.item.heartRate} bpm\n等级：${bloodPressureLevel.value}`
        : `血压测量记录\n时间：${props.item.measureTime}\n状态：${getErrorCodeInfo(props.item.errorCode).title}`;
    
    // #ifdef H5
    if (navigator.share) {
        navigator.share({
            title: '血压测量结果',
            text: shareText
        });
    } else {
        // 复制到剪贴板
        uni.setClipboardData({
            data: shareText,
            success: () => {
                uni.showToast({
                    title: '已复制到剪贴板',
                    icon: 'success'
                });
            }
        });
    }
    // #endif
    
    // #ifdef APP-PLUS
    uni.share({
        provider: 'weixin',
        scene: 'WXSceneSession',
        type: 0,
        summary: shareText,
        success: () => {
            uni.showToast({
                title: '分享成功',
                icon: 'success'
            });
        },
        fail: () => {
            // 分享失败，复制到剪贴板
            uni.setClipboardData({
                data: shareText,
                success: () => {
                    uni.showToast({
                        title: '已复制到剪贴板',
                        icon: 'success'
                    });
                }
            });
        }
    });
    // #endif
};

// 删除数据
const deleteData = () => {
    uni.showModal({
        title: '确认删除',
        content: '确定要删除这条测量记录吗？',
        success: (res) => {
            if (res.confirm) {
                const success = deleteMeasurementData(props.item.id);
                if (success) {
                    uni.showToast({
                        title: '删除成功',
                        icon: 'success'
                    });
                    emit('refresh');
                } else {
                    uni.showToast({
                        title: '删除失败',
                        icon: 'error'
                    });
                }
            }
        }
    });
};
</script>

<style scoped lang="scss">
.data-item {
    padding: 24rpx;
    background: #ffffff;
    border-radius: 12rpx;
    margin-bottom: 16rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;
    
    .measure-time {
        font-size: 28rpx;
        color: #666666;
        font-weight: 500;
    }
    
    .status-badge {
        padding: 4rpx 12rpx;
        border-radius: 12rpx;
        font-size: 24rpx;
        font-weight: 500;
        
        &.status-success {
            background: rgba(82, 196, 26, 0.1);
            color: #52c41a;
        }
        
        &.status-error {
            background: rgba(255, 77, 79, 0.1);
            color: #ff4d4f;
        }
    }
}

.measurement-result {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;
    
    .blood-pressure {
        flex: 1;
        
        .bp-values {
            display: flex;
            align-items: baseline;
            margin-bottom: 8rpx;
            
            .systolic {
                font-size: 48rpx;
                font-weight: bold;
                color: #333333;
            }
            
            .separator {
                font-size: 36rpx;
                color: #666666;
                margin: 0 8rpx;
            }
            
            .diastolic {
                font-size: 48rpx;
                font-weight: bold;
                color: #333333;
            }
            
            .unit {
                font-size: 24rpx;
                color: #999999;
                margin-left: 8rpx;
            }
        }
        
        .bp-level {
            font-size: 28rpx;
            font-weight: 500;
        }
    }
    
    .heart-rate {
        display: flex;
        align-items: center;
        align-self: baseline;
        padding-top: 12rpx;
        
        .hr-icon {
            font-size: 32rpx;
            color: #ff4757;
            margin-right: 8rpx;
        }
        
        .hr-value {
            display: flex;
            align-items: baseline;
            
            .hr-number {
                font-size: 36rpx;
                font-weight: bold;
                color: #333333;
            }
            
            .hr-unit {
                font-size: 24rpx;
                color: #999999;
                margin-left: 4rpx;
            }
        }
    }
}

.error-result {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
    
    .error-icon {
        font-size: 32rpx;
        color: #ff4d4f;
        margin-right: 12rpx;
    }
    
    .error-info {
        flex: 1;
        
        .error-title {
            display: block;
            font-size: 32rpx;
            font-weight: 500;
            color: #ff4d4f;
            margin-bottom: 4rpx;
        }
        
        .error-message {
            display: block;
            font-size: 28rpx;
            color: #666666;
            line-height: 1.4;
        }
    }
}

.device-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 24rpx;
    color: #999999;
    margin-right: 12rpx;
    .device-name,
    .battery-info {
        display: flex;
        align-items: center;
        
        .device-icon,
        .battery-icon {
            margin-right: 4rpx;
        }
    }
}

.item-actions {
    display: flex;
    justify-content: flex-end;
    gap: 16rpx;
    
    .action-btn {
        display: flex;
        align-items: center;
        padding: 8rpx 16rpx;
        border-radius: 8rpx;
        font-size: 24rpx;
        cursor: pointer;
        transition: all 0.3s ease;
        
        .btn-icon {
            margin-right: 4rpx;
        }
        
        &.share-btn {
            background: rgba(24, 144, 255, 0.1);
            color: #1890ff;
            
            &:active {
                background: rgba(24, 144, 255, 0.2);
            }
        }
        
        &.delete-btn {
            background: rgba(255, 77, 79, 0.1);
            color: #ff4d4f;
            
            &:active {
                background: rgba(255, 77, 79, 0.2);
            }
        }
    }
}
</style>

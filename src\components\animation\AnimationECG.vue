<template>
    <view class="ecg-container">
        <svg class="ecg-svg" viewBox="0 0 400 60" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="ecgGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style="stop-color:#33DFEE;stop-opacity:0.3" />
                    <stop offset="50%" style="stop-color:#33DFEE;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#33DFEE;stop-opacity:0.3" />
                </linearGradient>
            </defs>
            <path
                :d="ecgPath"
                stroke="url(#ecgGradient)"
                stroke-width="2"
                fill="none"
                class="ecg-line"
            />
        </svg>
    </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';

const props = defineProps({
    animationHr: {
        type: Number,
        default: 60
    }
});

const animationOffset = ref(0);
let animationId = null;
let lastTime = 0;

// 生成随机变化因子
const getRandomVariation = (baseValue, variationPercent = 0.2) => {
    const variation = baseValue * variationPercent;
    return baseValue + (Math.random() - 20) * 2 * variation;
};

// 心电图路径数据
const ecgPath = computed(() => {
    const baseY = 30;
    const width = 400;
    const cycleWidth = 120; // 每个心跳周期的宽度

    let path = '';

    // 生成连续的心电图线条
    const totalCycles = Math.ceil(width / cycleWidth) + 1;

    for (let cycle = 0; cycle < totalCycles; cycle++) {
        const cycleOffset = cycle * cycleWidth - animationOffset.value;

        // 为每个心跳周期生成随机变化的波形
        const heartbeatPattern = [
            { x: 0, y: 0 },
            { x: 8, y: getRandomVariation(0, 0.1) },
            { x: 12, y: getRandomVariation(-3, 0.3) }, // P波开始
            { x: 18, y: getRandomVariation(-3, 0.4) }, // P波峰值
            { x: 24, y: getRandomVariation(0, 0.2) }, // P波结束
            { x: 30, y: getRandomVariation(0, 0.1) },
            { x: 35, y: getRandomVariation(2, 0.5) }, // Q波
            { x: 40, y: getRandomVariation(-18, 0.25) }, // R波峰值 - 主要峰值
            { x: 45, y: getRandomVariation(8, 0.4) }, // S波
            { x: 50, y: getRandomVariation(0, 0.2) },
            { x: 55, y: getRandomVariation(0, 0.1) },
            { x: 65, y: getRandomVariation(4, 0.3) }, // T波开始
            { x: 75, y: getRandomVariation(4, 0.4) }, // T波峰值
            { x: 85, y: getRandomVariation(0, 0.2) }, // T波结束
            { x: 120, y: getRandomVariation(0, 0.1) } // 周期结束
        ];

        heartbeatPattern.forEach((point, index) => {
            const x = point.x + cycleOffset;
            const y = baseY + point.y;

            // 只绘制在可视区域内的点
            if (x >= -cycleWidth && x <= width + cycleWidth) {
                if (path === '' || index === 0) {
                    path += `M ${x} ${y} `;
                } else {
                    path += `L ${x} ${y} `;
                }
            }
        });
    }

    return path;
});

const startAnimation = () => {
    const animate = (currentTime) => {
        if (lastTime === 0) lastTime = currentTime;
        const deltaTime = currentTime - lastTime;

        // 根据心率计算动画速度，添加轻微的随机变化
        const baseSpeed = (props.animationHr / 60) * 2;
        const speedVariation = getRandomVariation(1, 0.05); // 5%的速度变化
        const speed = baseSpeed * speedVariation;

        animationOffset.value += speed * (deltaTime / 16.67); // 标准化到60fps

        // 重置偏移量以保持循环
        if (animationOffset.value > 120) {
            animationOffset.value = 0;
        }

        lastTime = currentTime;
        animationId = requestAnimationFrame(animate);
    };
    animationId = requestAnimationFrame(animate);
};

onMounted(() => {
    if (props.animationHr > 0) {
        startAnimation();
    }
});

onUnmounted(() => {
    if (animationId) {
        cancelAnimationFrame(animationId);
    }
});
</script>

<style lang="scss" scoped>
.ecg-container {
    width: 100%;
    height: 60rpx;
    overflow: hidden;
    position: relative;
}

.ecg-svg {
    width: 100%;
    height: 100%;
    display: block;
}

.ecg-line {
    filter: drop-shadow(0 0 3px #33DFEE);
    stroke-linecap: round;
    stroke-linejoin: round;
}
</style>
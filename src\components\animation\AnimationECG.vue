<template>
    <view class="ecg-container">
        <svg class="ecg-svg" viewBox="0 0 400 60" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="ecgGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style="stop-color:#33DFEE;stop-opacity:0.3" />
                    <stop offset="50%" style="stop-color:#33DFEE;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#33DFEE;stop-opacity:0.3" />
                </linearGradient>
                <linearGradient id="fadeOutGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style="stop-color:white;stop-opacity:1" />
                    <stop offset="15%" style="stop-color:white;stop-opacity:0.8" />
                    <stop offset="30%" style="stop-color:white;stop-opacity:0" />
                    <stop offset="70%" style="stop-color:white;stop-opacity:0" />
                    <stop offset="85%" style="stop-color:white;stop-opacity:0.8" />
                    <stop offset="100%" style="stop-color:white;stop-opacity:1" />
                </linearGradient>
                <mask id="ecgMask">
                    <rect width="400" height="60" fill="url(#fadeOutGradient)" />
                </mask>
            </defs>
            <path
                :d="ecgPath"
                stroke="url(#ecgGradient)"
                stroke-width="2"
                fill="none"
                class="ecg-line"
                mask="url(#ecgMask)"
            />
        </svg>
        <!-- 左右边缘渐变遮罩 -->
        <view class="fade-overlay fade-left"></view>
        <view class="fade-overlay fade-right"></view>
    </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';

const props = defineProps({
    animationHr: {
        type: Number,
        default: 60
    }
});

const animationOffset = ref(0);
let animationId = null;
let lastTime = 0;

// 存储每个心跳周期的随机变化值
const cycleVariations = new Map();

// 生成稳定的随机变化因子（基于周期索引）
const getStableRandomVariation = (cycleIndex, pointIndex, baseValue, variationPercent = 0.2) => {
    const key = `${cycleIndex}_${pointIndex}`;

    if (!cycleVariations.has(key)) {
        const variation = baseValue * variationPercent;
        const randomValue = baseValue + (Math.random() - 0.5) * 2 * variation;
        cycleVariations.set(key, randomValue);
    }

    return cycleVariations.get(key);
};

// 清理过期的变化值
const cleanupOldVariations = (currentCycle) => {
    const keysToDelete = [];
    for (const key of cycleVariations.keys()) {
        const cycleIndex = parseInt(key.split('_')[0]);
        if (cycleIndex < currentCycle - 5) { // 保留最近5个周期
            keysToDelete.push(key);
        }
    }
    keysToDelete.forEach(key => cycleVariations.delete(key));
};

// 心电图路径数据
const ecgPath = computed(() => {
    const baseY = 30;
    const width = 400;
    const cycleWidth = 120; // 每个心跳周期的宽度

    let path = '';

    // 生成连续的心电图线条
    const totalCycles = Math.ceil(width / cycleWidth) + 1;
    const currentCycle = Math.floor(animationOffset.value / cycleWidth);

    // 清理过期的变化值
    cleanupOldVariations(currentCycle);

    for (let cycle = 0; cycle < totalCycles; cycle++) {
        const cycleOffset = cycle * cycleWidth - animationOffset.value;
        const absoluteCycle = currentCycle + cycle;

        // 基础心电图波形模板
        const basePattern = [
            { x: 0, y: 0, variation: 0 },
            { x: 8, y: 0, variation: 0.1 },
            { x: 12, y: -3, variation: 0.3 }, // P波开始
            { x: 18, y: -3, variation: 0.4 }, // P波峰值
            { x: 24, y: 0, variation: 0.2 }, // P波结束
            { x: 30, y: 0, variation: 0.1 },
            { x: 35, y: 2, variation: 0.5 }, // Q波
            { x: 40, y: -18, variation: 0.25 }, // R波峰值 - 主要峰值
            { x: 45, y: 8, variation: 0.4 }, // S波
            { x: 50, y: 0, variation: 0.2 },
            { x: 55, y: 0, variation: 0.1 },
            { x: 65, y: 4, variation: 0.3 }, // T波开始
            { x: 75, y: 4, variation: 0.4 }, // T波峰值
            { x: 85, y: 0, variation: 0.2 }, // T波结束
            { x: 120, y: 0, variation: 0.1 } // 周期结束
        ];

        basePattern.forEach((point, index) => {
            const x = point.x + cycleOffset;
            const y = baseY + getStableRandomVariation(absoluteCycle, index, point.y, point.variation);

            // 只绘制在可视区域内的点
            if (x >= -cycleWidth && x <= width + cycleWidth) {
                if (path === '' || index === 0) {
                    path += `M ${x} ${y} `;
                } else {
                    path += `L ${x} ${y} `;
                }
            }
        });
    }

    return path;
});

const startAnimation = () => {
    const animate = (currentTime) => {
        if (lastTime === 0) lastTime = currentTime;
        const deltaTime = currentTime - lastTime;

        // 根据心率计算动画速度（移除速度随机变化，保持稳定）
        const baseSpeed = (props.animationHr / 60) * 2;
        const speed = baseSpeed;

        animationOffset.value += speed * (deltaTime / 16.67); // 标准化到60fps

        // 重置偏移量以保持循环
        if (animationOffset.value > 120) {
            animationOffset.value = 0;
        }

        lastTime = currentTime;
        animationId = requestAnimationFrame(animate);
    };
    animationId = requestAnimationFrame(animate);
};

onMounted(() => {
    if (props.animationHr > 0) {
        startAnimation();
    }
});

onUnmounted(() => {
    if (animationId) {
        cancelAnimationFrame(animationId);
    }
});
</script>

<style lang="scss" scoped>
.ecg-container {
    width: 100%;
    height: 60rpx;
    overflow: hidden;
    position: relative;
}

.ecg-svg {
    width: 100%;
    height: 100%;
    display: block;
}

.ecg-line {
    filter: drop-shadow(0 0 3px #33DFEE);
    stroke-linecap: round;
    stroke-linejoin: round;
}

.fade-overlay {
    position: absolute;
    top: 0;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.fade-left {
    left: 0;
    width: 60rpx;
    background: linear-gradient(to right,
        rgba(255, 255, 255, 1) 0%,
        rgba(255, 255, 255, 0.8) 30%,
        rgba(255, 255, 255, 0.4) 60%,
        rgba(255, 255, 255, 0) 100%
    );
}

.fade-right {
    right: 0;
    width: 60rpx;
    background: linear-gradient(to left,
        rgba(255, 255, 255, 1) 0%,
        rgba(255, 255, 255, 0.8) 30%,
        rgba(255, 255, 255, 0.4) 60%,
        rgba(255, 255, 255, 0) 100%
    );
}
</style>
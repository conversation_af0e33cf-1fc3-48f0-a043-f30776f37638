<template>
    <view class="ecg-container">
        <svg class="ecg-svg" viewBox="0 0 200 60" xmlns="http://www.w3.org/2000/svg">
            <path 
                :d="ecgPath" 
                stroke="#33DFEE" 
                stroke-width="2" 
                fill="none"
                class="ecg-line"
            />
        </svg>
    </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';

const props = defineProps({
    animationHr: {
        type: Number,
        default: 60
    }
});

const animationOffset = ref(0);
let animationId = null;

// 心电图路径数据
const ecgPath = computed(() => {
    const baseY = 30;
    const amplitude = 20;
    const width = 750;
    
    // 心电图波形：P波-QRS波群-T波
    const heartbeatPattern = [
        { x: 0, y: 0 },
        { x: 10, y: -2 }, // P波
        { x: 15, y: 0 },
        { x: 20, y: 0 },
        { x: 25, y: 2 }, // Q波
        { x: 30, y: -15 }, // R波
        { x: 35, y: 8 }, // S波
        { x: 40, y: 0 },
        { x: 50, y: 3 }, // T波
        { x: 60, y: 0 },
        { x: 80, y: 0 }
    ];
    
    let path = `M 0 ${baseY}`;
    
    // 生成多个心跳周期
    for (let cycle = 0; cycle < 3; cycle++) {
        const cycleOffset = cycle * 80 + animationOffset.value;
        
        heartbeatPattern.forEach((point, index) => {
            const x = (point.x + cycleOffset) % width;
            const y = baseY + point.y;
            
            if (index === 0) {
                path += ` M ${x} ${y}`;
            } else {
                path += ` L ${x} ${y}`;
            }
        });
    }
    
    return path;
});

const startAnimation = () => {
    const animate = () => {
        animationOffset.value += 2;
        if (animationOffset.value > 200) {
            animationOffset.value = 0;
        }
        animationId = requestAnimationFrame(animate);
    };
    animate();
};

onMounted(() => {
    if (props.animationHr > 0) {
        startAnimation();
    }
});

onUnmounted(() => {
    if (animationId) {
        cancelAnimationFrame(animationId);
    }
});
</script>

<style lang="scss" scoped>
.ecg-container {
    width: 100%;
    height: 60rpx;
    margin: 0 20rpx;
}

.ecg-svg {
    width: 100%;
    height: 100%;
}

.ecg-line {
    filter: drop-shadow(0 0 3px #33DFEE);
}
</style>
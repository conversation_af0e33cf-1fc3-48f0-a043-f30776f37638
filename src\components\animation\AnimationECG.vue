<template>
    <view class="ecg-container">
        <view class="ecg-line" :style="{
            animationDuration: animationDuration + 's',
            '--animation-duration': animationDuration + 's'
        }"></view>
        <!-- 左右边缘渐变遮罩 -->
        <view class="fade-overlay fade-left"></view>
        <view class="fade-overlay fade-right"></view>
    </view>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
    animationHr: {
        type: Number,
        default: 60
    }
});

// 根据心率计算动画持续时间
const animationDuration = computed(() => {
    // 心率越高，动画越快
    return 60 / props.animationHr;
});
</script>

<style lang="scss" scoped>
.ecg-container {
    width: 100%;
    height: 60rpx;
    overflow: hidden;
    position: relative;
    background: transparent;
}

.ecg-line {
    width: 100%;
    height: 100%;
    position: relative;
    background: linear-gradient(90deg,
        transparent 0%,
        transparent 8%,
        #33DFEE 8%,
        #33DFEE 8.5%,
        transparent 8.5%,
        transparent 12%,
        #33DFEE 12%,
        #33DFEE 13%,
        transparent 13%,
        transparent 15%,
        #33DFEE 15%,
        #33DFEE 15.5%,
        transparent 15.5%,
        transparent 20%,
        #33DFEE 20%,
        #33DFEE 20.5%,
        transparent 20.5%,
        transparent 25%,
        #33DFEE 25%,
        #33DFEE 25.5%,
        transparent 25.5%,
        transparent 29%,
        #33DFEE 29%,
        #33DFEE 30%,
        transparent 30%,
        transparent 31%,
        #33DFEE 31%,
        #33DFEE 33%,
        transparent 33%,
        transparent 35%,
        #33DFEE 35%,
        #33DFEE 36%,
        transparent 36%,
        transparent 40%,
        #33DFEE 40%,
        #33DFEE 41%,
        transparent 41%,
        transparent 45%,
        #33DFEE 45%,
        #33DFEE 46%,
        transparent 46%,
        transparent 50%,
        #33DFEE 50%,
        #33DFEE 51%,
        transparent 51%,
        transparent 54%,
        #33DFEE 54%,
        #33DFEE 55%,
        transparent 55%,
        transparent 60%,
        #33DFEE 60%,
        #33DFEE 61%,
        transparent 61%,
        transparent 65%,
        #33DFEE 65%,
        #33DFEE 66%,
        transparent 66%,
        transparent 70%,
        #33DFEE 70%,
        #33DFEE 71%,
        transparent 71%,
        transparent 75%,
        #33DFEE 75%,
        #33DFEE 76%,
        transparent 76%,
        transparent 80%,
        #33DFEE 80%,
        #33DFEE 81%,
        transparent 81%,
        transparent 100%
    );
    background-size: 400rpx 2rpx;
    background-repeat: repeat-x;
    background-position: 0 50%;
    animation: ecg-scroll var(--animation-duration, 1s) linear infinite;
    filter: drop-shadow(0 0 6rpx rgba(51, 223, 238, 0.6));

    // 心电图波形叠加
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            // P波 - 小波
            radial-gradient(ellipse 30rpx 6rpx at 80rpx 45%, #33DFEE 0%, transparent 70%),
            // QRS波群 - 主波
            radial-gradient(ellipse 6rpx 35rpx at 160rpx 25%, #33DFEE 0%, transparent 70%),
            radial-gradient(ellipse 6rpx 15rpx at 170rpx 65%, #33DFEE 0%, transparent 70%),
            // T波 - 中波
            radial-gradient(ellipse 35rpx 10rpx at 280rpx 42%, #33DFEE 0%, transparent 70%);
        background-size: 400rpx 60rpx;
        background-repeat: repeat-x;
        animation: ecg-scroll var(--animation-duration, 1s) linear infinite;
        opacity: 0.9;
    }
}

@keyframes ecg-scroll {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-400rpx);
    }
}

.fade-overlay {
    position: absolute;
    top: 0;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.fade-left {
    left: 0;
    width: 40rpx;
    background: linear-gradient(to right,
        rgba(255, 255, 255, 1) 0%,
        rgba(255, 255, 255, 0.9) 20%,
        rgba(255, 255, 255, 0.6) 50%,
        rgba(255, 255, 255, 0.2) 80%,
        rgba(255, 255, 255, 0) 100%
    );
}

.fade-right {
    right: 0;
    width: 40rpx;
    background: linear-gradient(to left,
        rgba(255, 255, 255, 1) 0%,
        rgba(255, 255, 255, 0.9) 20%,
        rgba(255, 255, 255, 0.6) 50%,
        rgba(255, 255, 255, 0.2) 80%,
        rgba(255, 255, 255, 0) 100%
    );
}
</style>
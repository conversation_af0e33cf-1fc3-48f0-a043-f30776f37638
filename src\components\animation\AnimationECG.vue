<template>
    <view class="ecg-container">
        <canvas
            canvas-id="ecgCanvas"
            id="ecgCanvas"
            class="ecg-canvas"
            :style="{ width: canvasWidth + 'rpx', height: canvasHeight + 'rpx' }"
        ></canvas>
        <!-- 左右边缘渐变遮罩 -->
        <view class="fade-overlay fade-left"></view>
        <view class="fade-overlay fade-right"></view>
    </view>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';

const props = defineProps({
    animationHr: {
        type: Number,
        default: 60
    }
});

const animationOffset = ref(0);
let animationTimer = null;
let canvasContext = null;

// Canvas尺寸
const canvasWidth = ref(750); // rpx
const canvasHeight = ref(120); // rpx

// 存储每个心跳周期的随机变化值
const cycleVariations = new Map();

// 生成稳定的随机变化因子（基于周期索引）
const getStableRandomVariation = (cycleIndex, pointIndex, baseValue, variationPercent = 0.2) => {
    const key = `${cycleIndex}_${pointIndex}`;

    if (!cycleVariations.has(key)) {
        const variation = baseValue * variationPercent;
        const randomValue = baseValue + (Math.random() - 0.5) * 2 * variation;
        cycleVariations.set(key, randomValue);
    }

    return cycleVariations.get(key);
};

// 清理过期的变化值
const cleanupOldVariations = (currentCycle) => {
    const keysToDelete = [];
    for (const key of cycleVariations.keys()) {
        const cycleIndex = parseInt(key.split('_')[0]);
        if (cycleIndex < currentCycle - 5) { // 保留最近5个周期
            keysToDelete.push(key);
        }
    }
    keysToDelete.forEach(key => cycleVariations.delete(key));
};

// 绘制心电图
const drawECG = () => {
    if (!canvasContext) return;

    const canvasWidthPx = canvasWidth.value;
    const canvasHeightPx = canvasHeight.value;
    const baseY = canvasHeightPx / 2;
    const cycleWidth = 120; // 每个心跳周期的宽度

    // 清空画布
    canvasContext.clearRect(0, 0, canvasWidthPx, canvasHeightPx);

    // 设置线条样式
    canvasContext.setStrokeStyle('#33DFEE');
    canvasContext.setLineWidth(2);
    canvasContext.setLineCap('round');
    canvasContext.setLineJoin('round');

    // 设置阴影效果
    canvasContext.setShadow(0, 0, 6, '#33DFEE');

    // 生成连续的心电图线条
    const totalCycles = Math.ceil(canvasWidthPx / cycleWidth) + 1;
    const currentCycle = Math.floor(animationOffset.value / cycleWidth);

    // 清理过期的变化值
    cleanupOldVariations(currentCycle);

    canvasContext.beginPath();
    let isFirstPoint = true;

    for (let cycle = 0; cycle < totalCycles; cycle++) {
        const cycleOffset = cycle * cycleWidth - animationOffset.value;
        const absoluteCycle = currentCycle + cycle;

        // 基础心电图波形模板
        const basePattern = [
            { x: 0, y: 0, variation: 0 },
            { x: 8, y: 0, variation: 0.1 },
            { x: 12, y: -3, variation: 0.3 }, // P波开始
            { x: 18, y: -3, variation: 0.4 }, // P波峰值
            { x: 24, y: 0, variation: 0.2 }, // P波结束
            { x: 30, y: 0, variation: 0.1 },
            { x: 35, y: 2, variation: 0.5 }, // Q波
            { x: 40, y: -18, variation: 0.25 }, // R波峰值 - 主要峰值
            { x: 45, y: 8, variation: 0.4 }, // S波
            { x: 50, y: 0, variation: 0.2 },
            { x: 55, y: 0, variation: 0.1 },
            { x: 65, y: 4, variation: 0.3 }, // T波开始
            { x: 75, y: 4, variation: 0.4 }, // T波峰值
            { x: 85, y: 0, variation: 0.2 }, // T波结束
            { x: 120, y: 0, variation: 0.1 } // 周期结束
        ];

        basePattern.forEach((point, index) => {
            const x = point.x + cycleOffset;
            const y = baseY + getStableRandomVariation(absoluteCycle, index, point.y, point.variation);

            // 只绘制在可视区域内的点
            if (x >= -cycleWidth && x <= canvasWidthPx + cycleWidth) {
                if (isFirstPoint) {
                    canvasContext.moveTo(x, y);
                    isFirstPoint = false;
                } else {
                    canvasContext.lineTo(x, y);
                }
            }
        });
    }

    canvasContext.stroke();
    canvasContext.draw(); // uni-app需要调用draw()来实际绘制
};

// 初始化Canvas
const initCanvas = () => {
    // 使用uni-app的Canvas API
    canvasContext = uni.createCanvasContext('ecgCanvas');

    // 开始绘制
    drawECG();
};

const startAnimation = () => {
    // 清除之前的定时器
    if (animationTimer) {
        clearInterval(animationTimer);
    }

    // 根据心率计算动画速度和间隔
    const baseSpeed = (props.animationHr / 60) * 2;
    const frameInterval = 16; // 约60fps

    animationTimer = setInterval(() => {
        // 每帧移动的距离
        animationOffset.value += baseSpeed;

        // 重置偏移量以保持循环
        if (animationOffset.value > 120) {
            animationOffset.value = 0;
        }

        // 重新绘制
        drawECG();
    }, frameInterval);
};

// 监听动画偏移变化
watch(animationOffset, () => {
    drawECG();
});

onMounted(() => {
    setTimeout(() => {
        initCanvas();
        if (props.animationHr > 0) {
            startAnimation();
        }
    }, 100); // 延迟确保DOM渲染完成
});

onUnmounted(() => {
    if (animationTimer) {
        clearInterval(animationTimer);
        animationTimer = null;
    }
});
</script>

<style lang="scss" scoped>
.ecg-container {
    width: 100%;
    height: 60rpx;
    overflow: hidden;
    position: relative;
}

.ecg-canvas {
    width: 100%;
    height: 100%;
    display: block;
}

.fade-overlay {
    position: absolute;
    top: 0;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.fade-left {
    left: 0;
    width: 40rpx;
    background: linear-gradient(to right,
        rgba(255, 255, 255, 1) 0%,
        rgba(255, 255, 255, 0.9) 20%,
        rgba(255, 255, 255, 0.6) 50%,
        rgba(255, 255, 255, 0.2) 80%,
        rgba(255, 255, 255, 0) 100%
    );
}

.fade-right {
    right: 0;
    width: 40rpx;
    background: linear-gradient(to left,
        rgba(255, 255, 255, 1) 0%,
        rgba(255, 255, 255, 0.9) 20%,
        rgba(255, 255, 255, 0.6) 50%,
        rgba(255, 255, 255, 0.2) 80%,
        rgba(255, 255, 255, 0) 100%
    );
}
</style>
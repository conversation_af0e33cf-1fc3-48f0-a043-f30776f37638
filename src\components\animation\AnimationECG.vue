<template>
    <view class="ecg-container">
        <svg class="ecg-svg" viewBox="0 0 400 60" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="ecgGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style="stop-color:#33DFEE;stop-opacity:0.3" />
                    <stop offset="50%" style="stop-color:#33DFEE;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#33DFEE;stop-opacity:0.3" />
                </linearGradient>
            </defs>
            <path
                :d="ecgPath"
                stroke="url(#ecgGradient)"
                stroke-width="2"
                fill="none"
                class="ecg-line"
            />
        </svg>
    </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';

const props = defineProps({
    animationHr: {
        type: Number,
        default: 60
    }
});

const animationOffset = ref(0);
let animationId = null;
let lastTime = 0;

// 心电图路径数据
const ecgPath = computed(() => {
    const baseY = 30;
    const width = 400;
    const cycleWidth = 120; // 每个心跳周期的宽度

    // 心电图波形：P波-QRS波群-T波
    const heartbeatPattern = [
        { x: 0, y: 0 },
        { x: 8, y: 0 },
        { x: 12, y: -3 }, // P波开始
        { x: 18, y: -3 }, // P波峰值
        { x: 24, y: 0 }, // P波结束
        { x: 30, y: 0 },
        { x: 35, y: 2 }, // Q波
        { x: 40, y: -18 }, // R波峰值
        { x: 45, y: 8 }, // S波
        { x: 50, y: 0 },
        { x: 55, y: 0 },
        { x: 65, y: 4 }, // T波开始
        { x: 75, y: 4 }, // T波峰值
        { x: 85, y: 0 }, // T波结束
        { x: 120, y: 0 } // 周期结束
    ];

    let path = '';

    // 生成连续的心电图线条
    const totalCycles = Math.ceil(width / cycleWidth) + 1;

    for (let cycle = 0; cycle < totalCycles; cycle++) {
        const cycleOffset = cycle * cycleWidth - animationOffset.value;

        heartbeatPattern.forEach((point, index) => {
            const x = point.x + cycleOffset;
            const y = baseY + point.y;

            // 只绘制在可视区域内的点
            if (x >= -cycleWidth && x <= width + cycleWidth) {
                if (path === '' || index === 0) {
                    path += `M ${x} ${y} `;
                } else {
                    path += `L ${x} ${y} `;
                }
            }
        });
    }

    return path;
});

const startAnimation = () => {
    const animate = (currentTime) => {
        if (lastTime === 0) lastTime = currentTime;
        const deltaTime = currentTime - lastTime;

        // 根据心率计算动画速度
        const speed = (props.animationHr / 60) * 2; // 基础速度调整
        animationOffset.value += speed * (deltaTime / 16.67); // 标准化到60fps

        // 重置偏移量以保持循环
        if (animationOffset.value > 120) {
            animationOffset.value = 0;
        }

        lastTime = currentTime;
        animationId = requestAnimationFrame(animate);
    };
    animationId = requestAnimationFrame(animate);
};

onMounted(() => {
    if (props.animationHr > 0) {
        startAnimation();
    }
});

onUnmounted(() => {
    if (animationId) {
        cancelAnimationFrame(animationId);
    }
});
</script>

<style lang="scss" scoped>
.ecg-container {
    width: 100%;
    height: 60rpx;
    overflow: hidden;
    position: relative;
}

.ecg-svg {
    width: 100%;
    height: 100%;
    display: block;
}

.ecg-line {
    filter: drop-shadow(0 0 3px #33DFEE);
    stroke-linecap: round;
    stroke-linejoin: round;
}
</style>
<template>
    <view>
        <view class="last-title">
            最近数据
            <view class="title-actions">
                <view class="action-btn refresh-btn" @click="refreshData">
                    <text class="btn-icon">🔄</text>
                    <text class="btn-text">刷新</text>
                </view>
                <view class="action-btn clear-btn ml-[20rpx]" @click="clearAllData">
                    <text class="btn-icon">🗑</text>
                    <text class="btn-text">清空</text>
                </view>
            </view>
        </view>

        <view class="list-box">
            <!-- 统计信息 -->
            <!-- <view v-if="dataList.length > 0" class="statistics-card">
                <view class="stats-title">统计概览</view>
                <view class="stats-content">
                    <view class="stat-item">
                        <text class="stat-label">总测量次数</text>
                        <text class="stat-value">{{ statistics.total }}</text>
                    </view>
                    <view class="stat-item">
                        <text class="stat-label">正常次数</text>
                        <text class="stat-value normal">{{ statistics.normalCount }}</text>
                    </view>
                    <view class="stat-item">
                        <text class="stat-label">异常次数</text>
                        <text class="stat-value abnormal">{{ statistics.highCount + statistics.lowCount }}</text>
                    </view>
                    <view class="stat-item">
                        <text class="stat-label">平均血压</text>
                        <text class="stat-value">{{ statistics.averageSystolic }}/{{ statistics.averageDiastolic }}</text>
                    </view>
                </view>
            </view> -->

            <!-- 数据列表 -->
            <view v-for="item in dataList" :key="item.id" class="data-item-wrapper">
                <dataItem :item="item" @refresh="refreshData"></dataItem>
            </view>

            <!-- 空状态 -->
            <view v-if="!dataList.length" class="empty-state">
                <view class="empty-icon">📊</view>
                <view class="empty-title">暂无测量数据</view>
                <view class="empty-desc">完成血压测量后，数据将显示在这里</view>
            </view>
        </view>
    </view>
</template>
<script setup lang="ts">
    import { ref, computed, onMounted } from 'vue';
    import {
        getMeasurementDataList,
        getMeasurementStatistics,
        clearAllMeasurementData,
        type MeasurementData
    } from '@/utils/measurementCache';
    import dataItem from './dataItem.vue';

    const dataList = ref<MeasurementData[]>([]);

    // 统计信息
    const statistics = computed(() => {
        return getMeasurementStatistics();
    });

    // 刷新数据
    const refreshData = () => {
        dataList.value = getMeasurementDataList();
        console.log('数据已刷新，共', dataList.value.length, '条记录');
    };

    // 清空所有数据
    const clearAllData = () => {
        uni.showModal({
            title: '确认清空',
            content: '确定要清空所有测量数据吗？此操作不可恢复。',
            success: (res) => {
                if (res.confirm) {
                    const success = clearAllMeasurementData();
                    if (success) {
                        refreshData();
                        uni.showToast({
                            title: '数据已清空',
                            icon: 'success'
                        });
                    } else {
                        uni.showToast({
                            title: '清空失败',
                            icon: 'error'
                        });
                    }
                }
            }
        });
    };

    // 组件挂载时加载数据
    onMounted(() => {
        refreshData();
    });

    defineExpose({
        refreshData
    })

</script>
<style scoped lang="scss">
.last-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #00DCA7;
    font-size: 48rpx;
    font-weight: 500;
    padding-bottom: 20rpx;

    .title-actions {
        display: flex;
        gap: 16rpx;

        .action-btn {
            display: flex;
            align-items: center;
            padding: 8rpx 16rpx;
            border-radius: 8rpx;
            font-size: 24rpx;
            cursor: pointer;
            transition: all 0.3s ease;

            .btn-icon {
                margin-right: 4rpx;
            }

            &.refresh-btn {
                background: rgba(24, 144, 255, 0.1);
                color: #1890ff;

                &:active {
                    background: rgba(24, 144, 255, 0.2);
                }
            }

            &.clear-btn {
                background: rgba(255, 77, 79, 0.1);
                color: #ff4d4f;

                &:active {
                    background: rgba(255, 77, 79, 0.2);
                }
            }
        }
    }
}

.list-box {
    border-radius: 16rpx;
    background: #ffffff;
    min-height: 600rpx;
    box-shadow: 0px 4rpx 20rpx rgba(42, 130, 228, 0.15);
    margin-bottom: 32rpx;
    padding: 24rpx;
}

.statistics-card {
    background: linear-gradient(135deg, #667eea 0%, #6d5fad 100%);
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 24rpx;
    color: white;

    .stats-title {
        font-size: 32rpx;
        font-weight: 600;
        margin-bottom: 16rpx;
        text-align: center;
    }

    .stats-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16rpx;

        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16rpx;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12rpx;

            .stat-label {
                font-size: 24rpx;
                opacity: 0.8;
                margin-bottom: 8rpx;
            }

            .stat-value {
                font-size: 32rpx;
                font-weight: bold;

                &.normal {
                    color: #52c41a;
                }

                &.abnormal {
                    color: #ff7875;
                }
            }
        }
    }
}

.data-item-wrapper {
    margin-bottom: 16rpx;

    &:last-child {
        margin-bottom: 0;
    }
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80rpx 40rpx;
    text-align: center;

    .empty-icon {
        font-size: 120rpx;
        margin-bottom: 24rpx;
        opacity: 0.3;
    }

    .empty-title {
        font-size: 36rpx;
        font-weight: 500;
        color: #666666;
        margin-bottom: 12rpx;
    }

    .empty-desc {
        font-size: 28rpx;
        color: #999999;
        line-height: 1.5;
    }
}
</style>
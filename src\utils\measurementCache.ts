// 测量数据缓存管理工具
export interface MeasurementData {
    id: string;
    systolic: number;      // 收缩压
    diastolic: number;     // 舒张压
    heartRate: number;     // 心率
    heartStatus: number;   // 心跳状态
    errorCode: number;     // 错误码
    timestamp: number;     // 时间戳
    measureTime: string;   // 格式化的测量时间
    deviceName?: string;   // 设备名称
    batteryLevel?: number; // 电池电量
    deviceId?: string;     // 设备id
}

const CACHE_KEY = 'measurement_data_cache';
const MAX_CACHE_SIZE = 10; // 最多缓存10条数据

/**
 * 生成唯一ID
 */
function generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

/**
 * 格式化时间
 */
function formatTime(timestamp: number): string {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}`;
}

/**
 * 获取血压等级描述
 */
function getBloodPressureLevel(systolic: number, diastolic: number): string {
    if (systolic < 90 || diastolic < 60) {
        return '低血压';
    } else if (systolic < 120 && diastolic < 80) {
        return '正常';
    } else if (systolic < 130 && diastolic < 80) {
        return '正常高值';
    } else if (systolic < 140 || diastolic < 90) {
        return '1级高血压';
    } else if (systolic < 160 || diastolic < 100) {
        return '2级高血压';
    } else {
        return '3级高血压';
    }
}

/**
 * 保存测量数据到缓存
 */
export function saveMeasurementData(data: Partial<MeasurementData>): boolean {
    try {
        // 获取现有缓存数据
        const existingData = getMeasurementDataList();
        
        // 创建新的测量数据
        const newData: MeasurementData = {
            id: generateId(),
            systolic: data.systolic || 0,
            diastolic: data.diastolic || 0,
            heartRate: data.heartRate || 0,
            heartStatus: data.heartStatus || 0,
            errorCode: data.errorCode || 0,
            timestamp: data.timestamp || Date.now(),
            measureTime: formatTime(data.timestamp || Date.now()),
            deviceName: data.deviceName || 'HYO2',
            deviceId: data.deviceId,
            batteryLevel: data.batteryLevel || 0
        };
        
        // 将新数据添加到列表开头
        existingData.unshift(newData);
        
        // 限制缓存大小
        if (existingData.length > MAX_CACHE_SIZE) {
            existingData.splice(MAX_CACHE_SIZE);
        }
        
        // 保存到本地存储
        uni.setStorageSync(CACHE_KEY, existingData);
        
        console.log('测量数据已保存到缓存:', newData);
        return true;
    } catch (error) {
        console.error('保存测量数据失败:', error);
        return false;
    }
}

/**
 * 获取所有测量数据
 */
export function getMeasurementDataList(): MeasurementData[] {
    try {
        const data = uni.getStorageSync(CACHE_KEY);
        return Array.isArray(data) ? data : [];
    } catch (error) {
        console.error('获取测量数据失败:', error);
        return [];
    }
}

/**
 * 获取最新的测量数据
 */
export function getLatestMeasurementData(): MeasurementData | null {
    const dataList = getMeasurementDataList();
    return dataList.length > 0 ? dataList[0] : null;
}

/**
 * 根据ID获取测量数据
 */
export function getMeasurementDataById(id: string): MeasurementData | null {
    const dataList = getMeasurementDataList();
    return dataList.find(item => item.id === id) || null;
}

/**
 * 删除指定ID的测量数据
 */
export function deleteMeasurementData(id: string): boolean {
    try {
        const dataList = getMeasurementDataList();
        const filteredList = dataList.filter(item => item.id !== id);
        
        uni.setStorageSync(CACHE_KEY, filteredList);
        console.log('测量数据已删除:', id);
        return true;
    } catch (error) {
        console.error('删除测量数据失败:', error);
        return false;
    }
}

/**
 * 清空所有测量数据
 */
export function clearAllMeasurementData(): boolean {
    try {
        uni.removeStorageSync(CACHE_KEY);
        console.log('所有测量数据已清空');
        return true;
    } catch (error) {
        console.error('清空测量数据失败:', error);
        return false;
    }
}

/**
 * 获取统计信息
 */
export function getMeasurementStatistics() {
    const dataList = getMeasurementDataList();
    
    if (dataList.length === 0) {
        return {
            total: 0,
            normalCount: 0,
            highCount: 0,
            lowCount: 0,
            averageSystolic: 0,
            averageDiastolic: 0,
            averageHeartRate: 0
        };
    }
    
    // 过滤掉错误的测量数据
    const validData = dataList.filter(item => item.errorCode === 0);
    
    if (validData.length === 0) {
        return {
            total: dataList.length,
            normalCount: 0,
            highCount: 0,
            lowCount: 0,
            averageSystolic: 0,
            averageDiastolic: 0,
            averageHeartRate: 0
        };
    }
    
    let normalCount = 0;
    let highCount = 0;
    let lowCount = 0;
    let totalSystolic = 0;
    let totalDiastolic = 0;
    let totalHeartRate = 0;
    
    validData.forEach(item => {
        const level = getBloodPressureLevel(item.systolic, item.diastolic);
        if (level === '正常' || level === '正常高值') {
            normalCount++;
        } else if (level.includes('高血压')) {
            highCount++;
        } else if (level === '低血压') {
            lowCount++;
        }
        
        totalSystolic += item.systolic;
        totalDiastolic += item.diastolic;
        totalHeartRate += item.heartRate;
    });
    
    return {
        total: dataList.length,
        normalCount,
        highCount,
        lowCount,
        averageSystolic: Math.round(totalSystolic / validData.length),
        averageDiastolic: Math.round(totalDiastolic / validData.length),
        averageHeartRate: Math.round(totalHeartRate / validData.length)
    };
}

/**
 * 导出测量数据（用于分享或备份）
 */
export function exportMeasurementData(): string {
    const dataList = getMeasurementDataList();
    return JSON.stringify(dataList, null, 2);
}

/**
 * 导入测量数据（用于恢复备份）
 */
export function importMeasurementData(jsonData: string): boolean {
    try {
        const data = JSON.parse(jsonData);
        if (Array.isArray(data)) {
            uni.setStorageSync(CACHE_KEY, data);
            console.log('测量数据导入成功');
            return true;
        } else {
            console.error('导入数据格式错误');
            return false;
        }
    } catch (error) {
        console.error('导入测量数据失败:', error);
        return false;
    }
}

// 血压测量错误码处理工具
export interface ErrorCodeInfo {
    code: number;
    title: string;
    message: string;
    type: 'success' | 'warning' | 'error' | 'info';
    source: 'device' | 'software';
    suggestion?: string;
}

// 错误码映射表
export const ERROR_CODE_MAP: Record<number, ErrorCodeInfo> = {
    // 正常状态
    0: {
        code: 0,
        title: '测量正常',
        message: '测量完成，结果正常',
        type: 'success',
        source: 'device'
    },
    
    // 设备端错误码 (万康发出)
    1: {
        code: 1,
        title: '脉搏检测失败',
        message: '测量不到有效的脉搏信号',
        type: 'warning',
        source: 'device',
        suggestion: '请保持安静，确保袖带佩戴正确，重新测量'
    },
    2: {
        code: 2,
        title: '充气异常',
        message: '11秒内打气不上50mmHg，可能是气袋没绑好',
        type: 'error',
        source: 'device',
        suggestion: '请检查袖带是否正确佩戴，确保袖带紧贴手臂'
    },
    3: {
        code: 3,
        title: '测量数值异常',
        message: '测量结果数值有误',
        type: 'error',
        source: 'device',
        suggestion: '请重新测量，如问题持续请联系客服'
    },
    4: {
        code: 4,
        title: '超压保护',
        message: '气袋压力超过295mmHg，进入超压保护',
        type: 'error',
        source: 'device',
        suggestion: '设备已自动停止测量以保护安全，请稍后重试'
    },
    5: {
        code: 5,
        title: '干预过多',
        message: '测量中检测到过多干预（移动、说话等）',
        type: 'warning',
        source: 'device',
        suggestion: '测量时请保持安静，避免移动和说话'
    },
    
    // 软件端错误码 (我们软件判断)
    10: {
        code: 10,
        title: '传感器故障',
        message: '两个压力传感器压力值差异过大，传感器可能出现故障',
        type: 'error',
        source: 'software',
        suggestion: '设备可能存在硬件故障，请联系客服检修'
    },
    11: {
        code: 11,
        title: '模块唤醒失败',
        message: '万康模块唤醒失败',
        type: 'error',
        source: 'software',
        suggestion: '请重启设备或重新连接蓝牙'
    },
    12: {
        code: 12,
        title: '模块休眠失败',
        message: '万康模块休眠失败',
        type: 'error',
        source: 'software',
        suggestion: '请重启设备或重新连接蓝牙'
    }
};

/**
 * 获取错误码信息
 * @param errorCode 错误码
 * @returns 错误码信息对象
 */
export function getErrorCodeInfo(errorCode: number): ErrorCodeInfo {
    return ERROR_CODE_MAP[errorCode] || {
        code: errorCode,
        title: '未知错误',
        message: `未知错误码: ${errorCode}`,
        type: 'error',
        source: 'device',
        suggestion: '请联系客服获取帮助'
    };
}

/**
 * 显示错误码对应的Toast提示
 * @param errorCode 错误码
 */
export function showErrorCodeToast(errorCode: number): void {
    const errorInfo = getErrorCodeInfo(errorCode);
    
    if (errorCode === 0) {
        uni.showToast({
            title: errorInfo.message,
            icon: 'success',
            duration: 2000
        });
        return;
    }
    
    // 根据错误类型显示不同的提示
    switch (errorInfo.type) {
        case 'warning':
            uni.showToast({
                title: errorInfo.message,
                icon: 'none',
                duration: 3000
            });
            break;
        case 'error':
            uni.showModal({
                title: errorInfo.title,
                content: errorInfo.message + (errorInfo.suggestion ? `\n\n建议：${errorInfo.suggestion}` : ''),
                showCancel: false,
                confirmText: '知道了'
            });
            break;
        default:
            uni.showToast({
                title: errorInfo.message,
                icon: 'none',
                duration: 2000
            });
    }
}

/**
 * 获取错误码的颜色样式
 * @param errorCode 错误码
 * @returns CSS颜色值
 */
export function getErrorCodeColor(errorCode: number): string {
    const errorInfo = getErrorCodeInfo(errorCode);
    
    switch (errorInfo.type) {
        case 'success':
            return '#52c41a';
        case 'warning':
            return '#faad14';
        case 'error':
            return '#ff4d4f';
        case 'info':
        default:
            return '#1890ff';
    }
}

/**
 * 判断是否为严重错误（需要停止测量）
 * @param errorCode 错误码
 * @returns 是否为严重错误
 */
export function isCriticalError(errorCode: number): boolean {
    const criticalErrors = [2, 3, 4, 10, 11, 12];
    return criticalErrors.includes(errorCode);
}